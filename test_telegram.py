"""
Test script để kiểm tra Telegram bot
"""

import asyncio
import sys

async def test_telegram():
    """Test gửi tin nhắn Telegram"""
    try:
        from telegram import Bot
        
        # Config
        TELEGRAM_TOKEN = "**********************************************"
        CHAT_ID = "-4747894787"
        
        # Tạo bot
        bot = Bot(token=TELEGRAM_TOKEN)
        
        # Test message
        message = """
🧪 <b>TELEGRAM BOT TEST</b>

✅ Bot đã kết nối thành công!
🤖 Trading signals sẽ được gửi vào group này.

📊 <b>Test Data:</b>
• BTC Price: $105,000
• Signal: LONG
• Time: Test Mode

🚀 Bot sẵn sàng hoạt động!
"""
        
        # Gửi tin nhắn
        await bot.send_message(
            chat_id=CHAT_ID,
            text=message.strip(),
            parse_mode='HTML'
        )
        
        print("✅ Test thành công! Kiểm tra group Telegram.")
        return True
        
    except ImportError:
        print("❌ Chưa cài đặt python-telegram-bot")
        print("💡 Chạy: pip install python-telegram-bot")
        return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        print("💡 Kiểm tra:")
        print("   - Bot token có đúng không?")
        print("   - Bot đã được add vào group chưa?")
        print("   - Group ID có đúng không?")
        return False

if __name__ == "__main__":
    print("🧪 Testing Telegram Bot...")
    result = asyncio.run(test_telegram())
    
    if result:
        print("\n🎉 Test hoàn tất! Bot sẵn sàng hoạt động.")
    else:
        print("\n❌ Test thất bại! Kiểm tra lại cấu hình.")
