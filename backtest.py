"""
Backtest Engine cho chiến lược MACD + RSI Multi-Timeframe
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import DataFetcher
from indicators import TechnicalIndicators, MultiTimeframeAnalysis
from strategy import MacdRsiStrategy, Trade
from basic_strategy import BasicMacdStrategy
import config

class BacktestEngine:
    """Engine để chạy backtest chiến lược"""
    
    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.data_fetcher = DataFetcher()
        self.strategy = BasicMacdStrategy(initial_capital)  # Sử dụng BasicMacdStrategy
        
    def prepare_data(self, symbol: str, lookback_days: int = 365) -> Dict[str, pd.DataFrame]:
        """
        Chuẩn bị dữ liệu cho backtest
        
        Args:
            symbol: Cặp giao dịch
            lookback_days: Số ngày dữ liệu
            
        Returns:
            Dict chứa dữ liệu các timeframe với indicators
        """
        print(f"📊 Chuẩn bị dữ liệu cho {symbol}...")
        
        # Tính toán số nến cần thiết cho mỗi timeframe
        timeframe_limits = {
            'HTF': lookback_days,  # 1 nến/ngày
            'ITF': lookback_days * 6,  # 6 nến/ngày (4h)
            'LTF': lookback_days * 24  # 24 nến/ngày (1h)
        }
        
        # Lấy dữ liệu
        raw_data = {}
        for tf_name, tf_value in config.TIMEFRAMES.items():
            limit = timeframe_limits.get(tf_name, 1000)
            df = self.data_fetcher.fetch_ohlcv(symbol, tf_value, limit)
            
            if not df.empty and self.data_fetcher.validate_data(df):
                raw_data[tf_name] = df
            else:
                print(f"❌ Không thể lấy dữ liệu hợp lệ cho {tf_name}")
                return {}
        
        # Tính toán indicators cho mỗi timeframe
        processed_data = {}
        for tf_name, df in raw_data.items():
            print(f"🔧 Tính toán indicators cho {tf_name}...")
            df_with_indicators = TechnicalIndicators.calculate_all_indicators(df)
            processed_data[tf_name] = df_with_indicators
        
        # Đồng bộ thời gian - sử dụng LTF làm base
        if 'LTF' in processed_data:
            ltf_times = processed_data['LTF'].index
            
            # Align HTF và ITF với LTF
            for tf_name in ['HTF', 'ITF']:
                if tf_name in processed_data:
                    # Forward fill để có dữ liệu cho mọi timestamp LTF
                    aligned_df = processed_data[tf_name].reindex(
                        ltf_times, 
                        method='ffill'
                    )
                    processed_data[tf_name] = aligned_df
        
        print(f"✅ Dữ liệu đã sẵn sàng. LTF: {len(processed_data.get('LTF', []))} nến")
        return processed_data
    
    def run_backtest(self, symbol: str = config.SYMBOL, lookback_days: int = 365) -> Dict:
        """
        Chạy backtest
        
        Args:
            symbol: Cặp giao dịch
            lookback_days: Số ngày backtest
            
        Returns:
            Dict chứa kết quả backtest
        """
        print(f"🚀 Bắt đầu backtest {symbol} trong {lookback_days} ngày...")
        
        # Chuẩn bị dữ liệu
        data = self.prepare_data(symbol, lookback_days)
        if not data or 'LTF' not in data:
            print("❌ Không thể chuẩn bị dữ liệu")
            return {}
        
        ltf_data = data['LTF']
        total_bars = len(ltf_data)
        
        # Bỏ qua 200 nến đầu để indicators ổn định
        start_idx = 200
        if total_bars <= start_idx:
            print(f"❌ Không đủ dữ liệu. Cần ít nhất {start_idx} nến")
            return {}
        
        print(f"📈 Chạy backtest từ nến {start_idx} đến {total_bars}")
        
        # Chạy backtest
        for i in range(start_idx, total_bars):
            current_time = ltf_data.index[i]
            current_data = {
                'HTF': data['HTF'].iloc[:i+1] if 'HTF' in data else pd.DataFrame(),
                'ITF': data['ITF'].iloc[:i+1] if 'ITF' in data else pd.DataFrame(), 
                'LTF': data['LTF'].iloc[:i+1]
            }
            
            current_bar = ltf_data.iloc[i]
            
            # Kiểm tra stop loss / take profit trước
            self.strategy.check_stop_loss_take_profit(
                current_time, 
                current_bar['high'], 
                current_bar['low']
            )
            
            # Kiểm tra tín hiệu exit
            if self.strategy.open_trade is not None:
                if self.strategy.check_exit_signals(current_data):
                    self.strategy.close_position(
                        current_time, 
                        current_bar['close'], 
                        'exit_signal'
                    )
            
            # Kiểm tra tín hiệu entry (chỉ khi không có lệnh mở)
            if self.strategy.open_trade is None:
                entry_signal = self.strategy.check_entry_signals(current_data)
                if entry_signal:
                    self.strategy.open_position(
                        current_time,
                        current_bar['close'],
                        entry_signal,
                        current_data['LTF']
                    )
            
            # Progress update
            if i % 1000 == 0:
                progress = (i - start_idx) / (total_bars - start_idx) * 100
                print(f"⏳ Progress: {progress:.1f}% | Trades: {len(self.strategy.trades)} | Capital: ${self.strategy.current_capital:.2f}")
        
        # Đóng lệnh cuối nếu còn mở
        if self.strategy.open_trade is not None:
            final_price = ltf_data.iloc[-1]['close']
            final_time = ltf_data.index[-1]
            self.strategy.close_position(final_time, final_price, 'backtest_end')
        
        # Tính toán kết quả
        results = self.strategy.get_performance_metrics()
        results['symbol'] = symbol
        results['lookback_days'] = lookback_days
        results['total_bars'] = total_bars
        results['start_date'] = ltf_data.index[start_idx]
        results['end_date'] = ltf_data.index[-1]
        
        return results
    
    def print_results(self, results: Dict):
        """In kết quả backtest"""
        if not results:
            print("❌ Không có kết quả để hiển thị")
            return
        
        print("\n" + "="*60)
        print("📊 KẾT QUẢ BACKTEST CHIẾN LƯỢC MACD + RSI")
        print("="*60)
        
        print(f"🎯 Symbol: {results.get('symbol', 'N/A')}")
        print(f"📅 Thời gian: {results.get('start_date', 'N/A')} → {results.get('end_date', 'N/A')}")
        print(f"📊 Tổng số nến: {results.get('total_bars', 0):,}")
        print(f"💰 Vốn ban đầu: ${self.initial_capital:,.2f}")
        print(f"💰 Vốn cuối: ${results.get('final_capital', 0):,.2f}")
        
        print(f"\n📈 HIỆU SUẤT:")
        print(f"   • Tổng lợi nhuận: ${results.get('total_pnl', 0):,.2f}")
        print(f"   • Tỷ suất sinh lời: {results.get('total_return', 0)*100:.2f}%")
        print(f"   • Drawdown tối đa: {results.get('max_drawdown', 0)*100:.2f}%")
        
        print(f"\n🎲 GIAO DỊCH:")
        print(f"   • Tổng số giao dịch: {results.get('total_trades', 0)}")
        print(f"   • Giao dịch thắng: {results.get('winning_trades', 0)}")
        print(f"   • Giao dịch thua: {results.get('losing_trades', 0)}")
        print(f"   • Tỷ lệ thắng: {results.get('win_rate', 0)*100:.2f}%")
        
        print(f"\n💡 CHỈ SỐ:")
        print(f"   • Profit Factor: {results.get('profit_factor', 0):.2f}")
        print(f"   • Lãi TB/giao dịch: ${results.get('avg_trade', 0):.2f}")
        print(f"   • Lãi TB khi thắng: ${results.get('avg_win', 0):.2f}")
        print(f"   • Lỗ TB khi thua: ${results.get('avg_loss', 0):.2f}")
        
        # Đánh giá chiến lược
        print(f"\n🎯 ĐÁNH GIÁ:")
        win_rate = results.get('win_rate', 0)
        profit_factor = results.get('profit_factor', 0)
        total_return = results.get('total_return', 0)
        max_dd = results.get('max_drawdown', 0)
        
        score = 0
        if win_rate >= 0.6:
            score += 25
        elif win_rate >= 0.5:
            score += 15
        
        if profit_factor >= 2.0:
            score += 25
        elif profit_factor >= 1.5:
            score += 15
        
        if total_return >= 0.5:
            score += 25
        elif total_return >= 0.2:
            score += 15
        
        if max_dd <= 0.15:
            score += 25
        elif max_dd <= 0.25:
            score += 15
        
        if score >= 80:
            rating = "🌟 XUẤT SẮC"
        elif score >= 60:
            rating = "✅ TỐT"
        elif score >= 40:
            rating = "⚠️ TRUNG BÌNH"
        else:
            rating = "❌ YẾU"
        
        print(f"   Điểm số: {score}/100 - {rating}")
        
        print("="*60)

if __name__ == "__main__":
    # Chạy backtest
    engine = BacktestEngine(initial_capital=config.BACKTEST_CONFIG['initial_capital'])
    
    results = engine.run_backtest(
        symbol=config.SYMBOL,
        lookback_days=config.BACKTEST_CONFIG['lookback_days']
    )
    
    engine.print_results(results)
