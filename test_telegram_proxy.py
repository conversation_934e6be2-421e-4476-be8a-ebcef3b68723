"""
Test Telegram bot với proxy cho VN
"""

import asyncio
import sys

async def test_telegram_with_proxy():
    """Test Telegram với proxy"""
    try:
        from telegram import Bo<PERSON>
        from telegram.request import HTTPXRequest
        from telegram.error import TelegramError
        
        # Config
        TELEGRAM_TOKEN = "**********************************************"
        CHAT_ID = "-4747894787"
        
        # Proxy config
        PROXY_URL = "http://*************:8888"
        
        print("🔍 Testing Telegram với proxy...")
        print(f"Proxy: {PROXY_URL}")
        print(f"Chat ID: {CHAT_ID}")
        
        # Setup proxy request
        proxy_request = HTTPXRequest(
            proxy=PROXY_URL,
            connection_pool_size=8,
            connect_timeout=20.0,
            read_timeout=20.0
        )
        
        # Tạo bot với proxy
        bot = Bot(token=TELEGRAM_TOKEN, request=proxy_request)
        
        # Test 1: Get bot info
        print("\n1️⃣ Testing bot info...")
        try:
            bot_info = await bot.get_me()
            print(f"✅ Bot info: @{bot_info.username} ({bot_info.first_name})")
        except TelegramError as e:
            print(f"❌ Bot info failed: {e}")
            return False
        
        # Test 2: Send message
        print("\n2️⃣ Testing send message...")
        try:
            test_message = """
🇻🇳 <b>TELEGRAM BOT TEST VỚI PROXY</b>

✅ Kết nối thành công qua proxy!
🌐 Proxy: *************:8888

🤖 <b>Bot Trading sẵn sàng hoạt động!</b>

📊 Sẽ gửi tín hiệu Bitcoin MACD + RSI vào group này.

🚀 <i>Made in Vietnam with ❤️</i>
"""
            
            message = await bot.send_message(
                chat_id=CHAT_ID,
                text=test_message.strip(),
                parse_mode='HTML'
            )
            print(f"✅ Message sent! ID: {message.message_id}")
            return True
            
        except TelegramError as e:
            print(f"❌ Send message failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Chạy: pip install python-telegram-bot")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_with_requests_proxy():
    """Test với requests + proxy"""
    print("\n🔄 Testing với requests + proxy...")
    
    try:
        import requests
        
        TELEGRAM_TOKEN = "**********************************************"
        CHAT_ID = "-4747894787"
        
        # Proxy config
        proxies = {
            'http': 'http://*************:8888',
            'https': 'http://*************:8888'
        }
        
        # Test message
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': CHAT_ID,
            'text': '🧪 Test message qua requests + proxy từ VN! 🇻🇳',
            'parse_mode': 'HTML'
        }
        
        response = requests.post(
            url, 
            data=data, 
            proxies=proxies,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                print("✅ Requests + proxy successful!")
                print(f"Message ID: {result['result']['message_id']}")
                return True
            else:
                print(f"❌ Telegram API error: {result}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Requests method failed: {e}")
        return False

def create_proxy_bot_script():
    """Tạo script bot với proxy"""
    print("\n📝 Tạo script bot với proxy...")
    
    script_content = '''"""
Telegram Trading Bot với Proxy cho VN
"""

import asyncio
import requests
import time
from datetime import datetime
import pandas as pd
import ccxt

# Config
TELEGRAM_TOKEN = "**********************************************"
CHAT_ID = "-4747894787"

# Proxy config
PROXIES = {
    'http': 'http://*************:8888',
    'https': 'http://*************:8888'
}

def send_telegram_message(message):
    """Gửi tin nhắn qua requests + proxy"""
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': CHAT_ID,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(
            url, 
            data=data, 
            proxies=PROXIES,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                print(f"✅ Message sent: {message[:50]}...")
                return True
        
        print(f"❌ Failed to send: {response.text}")
        return False
        
    except Exception as e:
        print(f"❌ Error sending message: {e}")
        return False

def main():
    """Main bot loop"""
    print("🤖 Starting Telegram Trading Bot với Proxy...")
    
    # Send startup message
    startup_msg = """
🚀 <b>BITCOIN TRADING BOT STARTED</b>

🇻🇳 Kết nối từ Vietnam qua proxy
📊 Strategy: MACD Crossover
⏰ Check interval: 5 minutes

🤖 Bot sẽ gửi tín hiệu trading vào group!
"""
    
    send_telegram_message(startup_msg.strip())
    
    while True:
        try:
            # Placeholder cho trading logic
            print("🔍 Checking market...")
            
            # Gửi test message mỗi 30 phút
            test_msg = f"""
📊 <b>Market Update</b>

💰 BTC: $105,000 (Test)
📈 Status: Monitoring...
⏰ {datetime.now().strftime('%H:%M:%S')}

🤖 Bot đang hoạt động bình thường!
"""
            
            send_telegram_message(test_msg.strip())
            
            # Chờ 30 phút
            time.sleep(1800)
            
        except KeyboardInterrupt:
            print("\\n🛑 Bot stopped!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(60)

if __name__ == "__main__":
    main()
'''
    
    with open('simple_telegram_bot.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Đã tạo file: simple_telegram_bot.py")
    print("🚀 Chạy: python simple_telegram_bot.py")

if __name__ == "__main__":
    print("🇻🇳 TELEGRAM BOT TEST VỚI PROXY")
    print("=" * 40)
    
    # Test method 1: python-telegram-bot với proxy
    result1 = asyncio.run(test_telegram_with_proxy())
    
    if not result1:
        # Test method 2: requests với proxy
        result2 = asyncio.run(test_with_requests_proxy())
        
        if not result2:
            print("\\n❌ Cả 2 method đều thất bại!")
            print("💡 Thử proxy khác hoặc kiểm tra network")
        else:
            print("\\n✅ Requests method hoạt động!")
            create_proxy_bot_script()
    else:
        print("\\n🎉 python-telegram-bot với proxy hoạt động!")
        print("🚀 Có thể chạy bot chính!")
'''
    
    try:
        with open('simple_telegram_bot.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("✅ Đã tạo simple_telegram_bot.py")
    except Exception as e:
        print(f"❌ Lỗi tạo file: {e}")
