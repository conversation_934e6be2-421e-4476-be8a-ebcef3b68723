"""
Telegram Trading Bot - Gửi tín hiệu MACD + RSI real-time
"""

import asyncio
import logging
from datetime import datetime, timedelta
import pandas as pd
from telegram import Bo<PERSON>
from telegram.error import TelegramError
import ccxt
import time
import traceback

from basic_strategy import BasicMacdStrategy
from indicators import TechnicalIndicators
import config

# Cấu hình Telegram
TELEGRAM_TOKEN = "**********************************************"
CHAT_ID = "-4747894787"  # Group ID

# Cấu hình Proxy (cho VN)
PROXY_CONFIG = {
    'proxy_url': 'http://173.249.25.36:8888',
    'proxy_type': 'http'
}

# Cấu hình Bot
CHECK_INTERVAL = 300  # 5 phút check một lần
LOOKBACK_CANDLES = 200  # Số nến để phân tích

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingBot:
    """Bot giao dịch Telegram"""
    
    def __init__(self):
        # Tạo bot với proxy
        from telegram.request import HTTPXRequest

        # Setup proxy request
        proxy_request = HTTPXRequest(
            proxy=PROXY_CONFIG['proxy_url'],
            connection_pool_size=8,
            connect_timeout=10.0,
            read_timeout=10.0
        )

        self.bot = Bot(token=TELEGRAM_TOKEN, request=proxy_request)
        self.exchange = ccxt.binance({'enableRateLimit': True})
        self.strategy = BasicMacdStrategy(10000)  # Virtual portfolio
        self.last_signal_time = None
        self.last_prices = {}
        
    async def send_message(self, message: str, parse_mode='HTML'):
        """Gửi tin nhắn vào group"""
        try:
            await self.bot.send_message(
                chat_id=CHAT_ID,
                text=message,
                parse_mode=parse_mode
            )
            logger.info(f"✅ Đã gửi tin nhắn: {message[:50]}...")
        except TelegramError as e:
            logger.error(f"❌ Lỗi gửi tin nhắn: {e}")
    
    def fetch_market_data(self):
        """Lấy dữ liệu thị trường"""
        try:
            # Lấy dữ liệu 4H cho signals
            data_4h = self.exchange.fetch_ohlcv(
                config.SYMBOL, '4h', limit=LOOKBACK_CANDLES
            )
            
            # Lấy dữ liệu 1H cho execution
            data_1h = self.exchange.fetch_ohlcv(
                config.SYMBOL, '1h', limit=LOOKBACK_CANDLES
            )
            
            # Convert to DataFrame
            df_4h = pd.DataFrame(data_4h, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df_1h = pd.DataFrame(data_1h, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Set timestamp as index
            for df in [df_4h, df_1h]:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                df[['open', 'high', 'low', 'close', 'volume']] = df[['open', 'high', 'low', 'close', 'volume']].astype(float)
            
            # Tính toán indicators
            df_4h_indicators = TechnicalIndicators.calculate_all_indicators(df_4h)
            df_1h_indicators = TechnicalIndicators.calculate_all_indicators(df_1h)
            
            return {
                'ITF': df_4h_indicators,
                'LTF': df_1h_indicators
            }
            
        except Exception as e:
            logger.error(f"❌ Lỗi lấy dữ liệu: {e}")
            return None
    
    def check_signals(self, data):
        """Kiểm tra tín hiệu giao dịch"""
        try:
            # Reset strategy để check signal mới
            self.strategy.open_trade = None
            
            # Kiểm tra entry signal
            signal = self.strategy.check_entry_signals(data)
            
            if signal:
                current_time = datetime.now()
                
                # Tránh spam signals (chỉ gửi 1 signal/30 phút)
                if (self.last_signal_time and 
                    current_time - self.last_signal_time < timedelta(minutes=30)):
                    return None
                
                self.last_signal_time = current_time
                return signal
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Lỗi check signals: {e}")
            return None
    
    def format_signal_message(self, signal: str, data: dict):
        """Format tin nhắn tín hiệu"""
        try:
            current_price = data['LTF']['close'].iloc[-1]
            
            # Lấy thông tin indicators
            itf_latest = data['ITF'].iloc[-1]
            ltf_latest = data['LTF'].iloc[-1]
            
            macd_4h = itf_latest.get('macd', 0)
            macd_signal_4h = itf_latest.get('macd_signal', 0)
            rsi_4h = itf_latest.get('rsi', 50)
            
            # Tính toán stop loss và take profit
            if signal == 'long':
                emoji = "🟢"
                direction = "LONG"
                atr = ltf_latest.get('atr', current_price * 0.02)
                stop_loss = current_price - (atr * 2)
                take_profit = current_price + (atr * 4)
            else:
                emoji = "🔴"
                direction = "SHORT"
                atr = ltf_latest.get('atr', current_price * 0.02)
                stop_loss = current_price + (atr * 2)
                take_profit = current_price - (atr * 4)
            
            # Tính risk/reward
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            rr_ratio = reward / risk if risk > 0 else 0
            
            message = f"""
{emoji} <b>BITCOIN TRADING SIGNAL</b> {emoji}

🎯 <b>Direction:</b> {direction}
💰 <b>Entry Price:</b> ${current_price:,.2f}
🛑 <b>Stop Loss:</b> ${stop_loss:,.2f}
🎯 <b>Take Profit:</b> ${take_profit:,.2f}
📊 <b>Risk/Reward:</b> 1:{rr_ratio:.1f}

📈 <b>Technical Analysis:</b>
• MACD (4H): {macd_4h:.2f}
• Signal (4H): {macd_signal_4h:.2f}
• RSI (4H): {rsi_4h:.1f}

⏰ <b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 <b>Strategy:</b> MACD Crossover

⚠️ <i>Risk Management: 2% per trade
Not financial advice. Trade at your own risk.</i>
"""
            
            return message.strip()
            
        except Exception as e:
            logger.error(f"❌ Lỗi format message: {e}")
            return f"{signal.upper()} Signal at ${data['LTF']['close'].iloc[-1]:,.2f}"
    
    def format_market_update(self, data: dict):
        """Format tin nhắn cập nhật thị trường"""
        try:
            current_price = data['LTF']['close'].iloc[-1]
            prev_price = self.last_prices.get('BTC', current_price)
            
            # Tính % thay đổi
            price_change = ((current_price - prev_price) / prev_price * 100) if prev_price else 0
            change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            
            # Lấy indicators
            itf_latest = data['ITF'].iloc[-1]
            ltf_latest = data['LTF'].iloc[-1]
            
            macd_4h = itf_latest.get('macd', 0)
            macd_signal_4h = itf_latest.get('macd_signal', 0)
            rsi_4h = itf_latest.get('rsi', 50)
            
            # Trend analysis
            if macd_4h > macd_signal_4h:
                trend = "🟢 Bullish"
            else:
                trend = "🔴 Bearish"
            
            # RSI analysis
            if rsi_4h > 70:
                rsi_status = "🔴 Overbought"
            elif rsi_4h < 30:
                rsi_status = "🟢 Oversold"
            else:
                rsi_status = "🟡 Neutral"
            
            message = f"""
📊 <b>BITCOIN MARKET UPDATE</b>

💰 <b>Price:</b> ${current_price:,.2f} {change_emoji} {price_change:+.2f}%

📈 <b>Technical Status:</b>
• Trend (4H): {trend}
• MACD: {macd_4h:.2f} / {macd_signal_4h:.2f}
• RSI: {rsi_4h:.1f} ({rsi_status})

⏰ {datetime.now().strftime('%H:%M:%S')}
"""
            
            # Update last price
            self.last_prices['BTC'] = current_price
            
            return message.strip()
            
        except Exception as e:
            logger.error(f"❌ Lỗi format market update: {e}")
            return f"BTC: ${data['LTF']['close'].iloc[-1]:,.2f}"
    
    async def run_bot(self):
        """Chạy bot chính"""
        logger.info("🤖 Bot Trading đã khởi động!")
        
        # Gửi tin nhắn khởi động
        startup_message = f"""
🚀 <b>BITCOIN TRADING BOT STARTED</b>

📊 <b>Strategy:</b> MACD + RSI Multi-Timeframe
⏰ <b>Check Interval:</b> {CHECK_INTERVAL//60} minutes
🎯 <b>Symbol:</b> {config.SYMBOL}

🤖 Bot sẽ gửi tín hiệu khi có cơ hội giao dịch!
"""
        await self.send_message(startup_message)
        
        market_update_counter = 0
        
        while True:
            try:
                logger.info("🔍 Đang kiểm tra thị trường...")
                
                # Lấy dữ liệu thị trường
                data = self.fetch_market_data()
                if not data:
                    logger.warning("⚠️ Không thể lấy dữ liệu, thử lại sau...")
                    await asyncio.sleep(60)
                    continue
                
                # Kiểm tra tín hiệu
                signal = self.check_signals(data)
                
                if signal:
                    logger.info(f"🎯 Phát hiện tín hiệu: {signal}")
                    message = self.format_signal_message(signal, data)
                    await self.send_message(message)
                
                # Gửi market update mỗi 1 giờ (12 lần check)
                market_update_counter += 1
                if market_update_counter >= 12:
                    market_message = self.format_market_update(data)
                    await self.send_message(market_message)
                    market_update_counter = 0
                
                logger.info(f"✅ Hoàn thành check. Chờ {CHECK_INTERVAL} giây...")
                await asyncio.sleep(CHECK_INTERVAL)
                
            except KeyboardInterrupt:
                logger.info("🛑 Bot đã dừng bởi người dùng")
                break
            except Exception as e:
                logger.error(f"❌ Lỗi không mong muốn: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(60)  # Chờ 1 phút trước khi thử lại

async def main():
    """Hàm main"""
    bot = TradingBot()
    await bot.run_bot()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Bot đã dừng!")
    except Exception as e:
        print(f"❌ Lỗi khởi động bot: {e}")
        traceback.print_exc()
