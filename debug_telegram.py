"""
Debug script để kiểm tra Telegram bot configuration
"""

import asyncio
import sys

async def debug_telegram():
    """Debug Telegram bot"""
    try:
        from telegram import Bo<PERSON>
        from telegram.error import TelegramError
        
        # Config
        TELEGRAM_TOKEN = "**********************************************"
        CHAT_ID = "-4747894787"
        
        print("🔍 Debugging Telegram Bot...")
        print(f"Token: {TELEGRAM_TOKEN[:10]}...")
        print(f"Chat ID: {CHAT_ID}")
        
        # Tạo bot
        bot = Bot(token=TELEGRAM_TOKEN)
        
        # Test 1: Get bot info
        print("\n1️⃣ Testing bot info...")
        try:
            bot_info = await bot.get_me()
            print(f"✅ Bot info: @{bot_info.username} ({bot_info.first_name})")
        except TelegramError as e:
            print(f"❌ Bot info failed: {e}")
            return False
        
        # Test 2: Get chat info
        print("\n2️⃣ Testing chat info...")
        try:
            chat_info = await bot.get_chat(chat_id=CHAT_ID)
            print(f"✅ Chat info: {chat_info.title} (Type: {chat_info.type})")
        except TelegramError as e:
            print(f"❌ Chat info failed: {e}")
            print("💡 Possible issues:")
            print("   - Bot chưa được add vào group")
            print("   - Group ID không đúng")
            print("   - Bot không có quyền trong group")
            return False
        
        # Test 3: Send message
        print("\n3️⃣ Testing send message...")
        try:
            message = await bot.send_message(
                chat_id=CHAT_ID,
                text="🧪 Debug test message - Bot hoạt động bình thường!"
            )
            print(f"✅ Message sent successfully! Message ID: {message.message_id}")
            return True
        except TelegramError as e:
            print(f"❌ Send message failed: {e}")
            return False
            
    except ImportError:
        print("❌ python-telegram-bot chưa được cài đặt")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_alternative_method():
    """Test với method khác"""
    print("\n🔄 Testing alternative method...")
    
    try:
        import requests
        
        TELEGRAM_TOKEN = "**********************************************"
        CHAT_ID = "-4747894787"
        
        # Test với requests
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': CHAT_ID,
            'text': '🧪 Test message via requests API'
        }
        
        response = requests.post(url, data=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ Requests method successful!")
            return True
        else:
            print(f"❌ Requests failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Requests method failed: {e}")
        return False

def print_setup_instructions():
    """In hướng dẫn setup"""
    print("\n📋 HƯỚNG DẪN SETUP TELEGRAM BOT:")
    print("=" * 50)
    print("1. Tạo bot mới:")
    print("   - Nhắn tin cho @BotFather")
    print("   - Gửi /newbot")
    print("   - Đặt tên và username cho bot")
    print("   - Lưu token")
    
    print("\n2. Add bot vào group:")
    print("   - Add bot vào group cần gửi tin")
    print("   - Cấp quyền admin cho bot")
    print("   - Hoặc ít nhất quyền gửi tin nhắn")
    
    print("\n3. Lấy Group ID:")
    print("   - Add @userinfobot vào group")
    print("   - Gửi /start")
    print("   - Bot sẽ trả về Group ID")
    
    print("\n4. Test bot:")
    print("   - Gửi tin nhắn test vào group")
    print("   - Kiểm tra bot có phản hồi không")

if __name__ == "__main__":
    print("🔍 TELEGRAM BOT DEBUG")
    print("=" * 30)
    
    # Test chính
    result = asyncio.run(debug_telegram())
    
    if not result:
        # Test phương pháp khác
        alt_result = asyncio.run(test_alternative_method())
        
        if not alt_result:
            print_setup_instructions()
    else:
        print("\n🎉 Telegram bot hoạt động bình thường!")
        print("🚀 Có thể chạy trading bot ngay!")
