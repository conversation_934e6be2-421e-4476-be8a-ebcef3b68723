"""
Test đơn giản Telegram với proxy
"""

import requests
import time

def test_telegram_proxy():
    """Test gửi tin nhắn qua proxy"""
    
    # Config
    TELEGRAM_TOKEN = "**********************************************"
    CHAT_ID = "-4747894787"
    
    # Proxy config
    proxies = {
        'http': 'http://173.249.25.36:8888',
        'https': 'http://173.249.25.36:8888'
    }
    
    print("🇻🇳 Testing Telegram từ VN với proxy...")
    print(f"Proxy: {proxies['http']}")
    
    # Test message
    message = """
🇻🇳 <b>TEST TỪ VIETNAM</b>

✅ Kết nối thành công qua proxy!
🤖 Bot Trading sẵn sàng!

📊 Sẽ gửi tín hiệu Bitcoin vào group này.

🚀 <i>Proxy working from VN!</i>
"""
    
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
        data = {
            'chat_id': CHAT_ID,
            'text': message.strip(),
            'parse_mode': 'HTML'
        }
        
        print("📤 Đang gửi tin nhắn...")
        
        response = requests.post(
            url, 
            data=data, 
            proxies=proxies,
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                print("✅ SUCCESS! Tin nhắn đã được gửi!")
                print(f"Message ID: {result['result']['message_id']}")
                return True
            else:
                print(f"❌ Telegram API error: {result}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 SIMPLE TELEGRAM PROXY TEST")
    print("=" * 35)
    
    success = test_telegram_proxy()
    
    if success:
        print("\n🎉 PROXY HOẠT ĐỘNG!")
        print("✅ Có thể tạo bot trading với proxy này!")
    else:
        print("\n❌ Proxy không hoạt động!")
        print("💡 Thử proxy khác hoặc kiểm tra network")
